PODS:
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 5.0)
  - FlutterMacOS (1.0.0)
  - OrderedSet (5.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (0.0.1):
    - Flutter
    - FlutterMacOS
    - Try
  - Try (2.1.1)

DEPENDENCIES:
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - speech_to_text (from `Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin`)

SPEC REPOS:
  trunk:
    - OrderedSet
    - Try

EXTERNAL SOURCES:
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  speech_to_text:
    :path: Flutter/ephemeral/.symlinks/plugins/speech_to_text/darwin

SPEC CHECKSUMS:
  flutter_inappwebview_macos: 9600c9df9fdb346aaa8933812009f8d94304203d
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  speech_to_text: 627d3fd2194770b51abb324ba45c2d39398f24a8
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.15.2
