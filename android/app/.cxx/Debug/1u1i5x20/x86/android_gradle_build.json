{"buildFiles": ["/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/android/app/.cxx/Debug/1u1i5x20/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ninja", "-C", "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/android/app/.cxx/Debug/1u1i5x20/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": []}