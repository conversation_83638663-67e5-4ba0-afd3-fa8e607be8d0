{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/android/app/.cxx/Debug/1u1i5x20/x86", "source": "/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}