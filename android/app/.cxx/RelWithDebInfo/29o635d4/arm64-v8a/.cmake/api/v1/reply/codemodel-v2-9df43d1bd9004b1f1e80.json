{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/android/app/.cxx/RelWithDebInfo/29o635d4/arm64-v8a", "source": "/Users/<USER>/Documents/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}