PODS:
  - asl_flutter_input (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - Firebase/Analytics (11.10.0):
    - Firebase/Core
  - Firebase/Core (11.10.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 11.10.0)
  - Firebase/CoreOnly (11.10.0):
    - FirebaseCore (~> 11.10.0)
  - Firebase/Database (11.10.0):
    - Firebase/CoreOnly
    - FirebaseDatabase (~> 11.10.0)
  - firebase_analytics (11.4.5):
    - Firebase/Analytics (= 11.10.0)
    - firebase_core
    - Flutter
  - firebase_core (3.13.0):
    - Firebase/CoreOnly (= 11.10.0)
    - Flutter
  - firebase_database (11.3.5):
    - Firebase/Database (= 11.10.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (11.10.0):
    - FirebaseAnalytics/AdIdSupport (= 11.10.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAnalytics/AdIdSupport (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleAppMeasurement (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - FirebaseAppCheckInterop (11.11.0)
  - FirebaseCore (11.10.0):
    - FirebaseCoreInternal (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/Logger (~> 8.0)
  - FirebaseCoreInternal (11.10.0):
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
  - FirebaseDatabase (11.10.0):
    - FirebaseAppCheckInterop (~> 11.0)
    - FirebaseCore (~> 11.10.0)
    - FirebaseSharedSwift (~> 11.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - leveldb-library (~> 1.22)
  - FirebaseInstallations (11.10.0):
    - FirebaseCore (~> 11.10.0)
    - GoogleUtilities/Environment (~> 8.0)
    - GoogleUtilities/UserDefaults (~> 8.0)
    - PromisesObjC (~> 2.4)
  - FirebaseSharedSwift (11.11.0)
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_unity_widget (4.0.0):
    - Flutter
  - GoogleAppMeasurement (11.10.0):
    - GoogleAppMeasurement/AdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/AdIdSupport (11.10.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 11.10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (11.10.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 8.0)
    - GoogleUtilities/MethodSwizzler (~> 8.0)
    - GoogleUtilities/Network (~> 8.0)
    - "GoogleUtilities/NSData+zlib (~> 8.0)"
    - nanopb (~> 3.30910.0)
  - GoogleUtilities/AppDelegateSwizzler (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.0.2):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.0.2):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.0.2):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.0.2)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.0.2)
  - GoogleUtilities/Reachability (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.0.2):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - leveldb-library (1.22.6)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - speech_to_text (0.0.1):
    - Flutter
    - FlutterMacOS
    - Try
  - Try (2.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - asl_flutter_input (from `.symlinks/plugins/asl_flutter_input/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_database (from `.symlinks/plugins/firebase_database/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_unity_widget (from `.symlinks/plugins/flutter_unity_widget/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - speech_to_text (from `.symlinks/plugins/speech_to_text/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseDatabase
    - FirebaseInstallations
    - FirebaseSharedSwift
    - GoogleAppMeasurement
    - GoogleUtilities
    - leveldb-library
    - nanopb
    - OrderedSet
    - PromisesObjC
    - Try

EXTERNAL SOURCES:
  asl_flutter_input:
    :path: ".symlinks/plugins/asl_flutter_input/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_database:
    :path: ".symlinks/plugins/firebase_database/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_unity_widget:
    :path: ".symlinks/plugins/flutter_unity_widget/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  speech_to_text:
    :path: ".symlinks/plugins/speech_to_text/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  asl_flutter_input: a6f7456e74ba67c249a54d127dbed8379ebba638
  audio_waveforms: a6dde7fe7c0ea05f06ffbdb0f7c1b2b2ba6cedcf
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Firebase: 1fe1c0a7d9aaea32efe01fbea5f0ebd8d70e53a2
  firebase_analytics: 1998960b8fa16fd0cd9e77a6f9fd35a2009ad65e
  firebase_core: 2d4534e7b489907dcede540c835b48981d890943
  firebase_database: 9206087343504a4554718494c5880360f3ce375b
  FirebaseAnalytics: 4e42333f02cf78ed93703a5c36f36dd518aebdef
  FirebaseAppCheckInterop: f23709c9ce92d810aa53ff4ce12ad3e666a3c7be
  FirebaseCore: 8344daef5e2661eb004b177488d6f9f0f24251b7
  FirebaseCoreInternal: ef4505d2afb1d0ebbc33162cb3795382904b5679
  FirebaseDatabase: 229b4959145d197dab99ab9d7033b6d4fab897a1
  FirebaseInstallations: 9980995bdd06ec8081dfb6ab364162bdd64245c3
  FirebaseSharedSwift: b1d32c3b29a911dc174bcf363f2f70bda9509d2f
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_unity_widget: 2ed697ec5fba610307189f51b1300864718c7d5f
  GoogleAppMeasurement: 36684bfb3ee034e2b42b4321eb19da3a1b81e65d
  GoogleUtilities: 26a3abef001b6533cf678d3eb38fd3f614b7872d
  leveldb-library: cc8b8f8e013647a295ad3f8cd2ddf49a6f19be19
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  speech_to_text: 9dc43a5df3cbc2813f8c7cc9bd0fbf94268ed7ac
  Try: 5ef669ae832617b3cee58cb2c6f99fb767a4ff96
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: 7be2f5f74864d463a8ad433546ed1de7e0f29aef

COCOAPODS: 1.16.2
