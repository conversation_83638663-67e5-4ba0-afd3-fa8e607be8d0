using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System; 

public class ArabAnimationController : MonoBehaviour
{
    public GameObject avatarCharacter;
    public Animator anim;
    public float speed = 1f;

    void Start()
    {
        anim = avatarCharacter.GetComponent<Animator>();
    }

    public void AdjustAnimationSpeed(string newSpeed)

    {
        Debug.Log($"Inside SetAnimationSpeed");
        if (float.TryParse(newSpeed, out float speedValue))
        {
            Debug.Log($"Converted speed value: {speedValue}");
        }
        else
        {
            Debug.LogError("Failed to parse speed value");
        }
        if (anim != null)
        {
            speed = Mathf.Clamp(speedValue, 0.01f, float.MaxValue);
            anim.speed = speed;
            Debug.Log($"Animation speed set to {speed}");
        }
        else
        {
            Debug.LogError("Animator not found!");
        }
    }

    public void SwitchAnimation(string animationName,  float delay = 0f, float transitionDuration = 0.3f)
    {
        StartCoroutine(SwitchAnimationCoroutine(animationName, delay, transitionDuration));
    }


    //IEnumerator SwitchAnimationCoroutine(string animationNames, float speed)
    //{

    //        anim.Play(animationNames);
    //        yield return new WaitForSeconds(0.5f);
    //        AnimatorStateInfo stateInfo = anim.GetCurrentAnimatorStateInfo(0);

    //        // Debug.Log("Length: " + stateInfo.length); //length of animation keyframe
    //        // Debug.Log("Animation " +i);

    //        anim.speed = speed;
    //        float duration = stateInfo.length + 0.3f; //length of key frame of each animation with some speed
    //        yield return new WaitForSeconds(duration);

    //        // Debug.Log("i= "+i);

    //        // Check animation is over from array
    //        //if (i == (animationNames.Length - 1))
    //        //{
    //        //    // Debug.Log("Animation is over");
    //        //    anim.speed = speed;
    //        //    anim.Play("Idle");
    //        //    yield return new WaitForSeconds(1f);
    //        //}


    //}

    IEnumerator SwitchAnimationCoroutine(string animationName, float delay, float transitionDuration)
    {
        yield return new WaitForSeconds(delay);

        // Reset character position and scale
        transform.position = Vector3.zero;
        transform.localScale = Vector3.one;

        anim.speed = speed;
        //anim.Play(animationName);
        anim.CrossFadeInFixedTime(animationName, transitionDuration);

        float animationLength = GetAnimationLength(animationName);
        Debug.Log($"Playing animation: {animationName}, Length: {animationLength}");

        if (animationLength > 0)
        {
            yield return new WaitForSeconds(animationLength);
        }
        else
        {
            Debug.LogWarning($"Animation '{animationName}' not found. Skipping...");
        }

        //yield return new WaitForSeconds(animationLength);
    }

    float GetAnimationLength(string animationName)
    {
        AnimationClip[] clips = anim.runtimeAnimatorController.animationClips;

        AnimationClip clip = Array.Find(clips, c => c.name == animationName);

        if (clip != null)
        {
            float duration = clip.length / anim.speed;
            Debug.Log($"Clip length: " + clip.length + "  duration: " + duration);
            return clip.length / anim.speed;
        }
        else
        {
            Debug.LogWarning($"Animation clip '{animationName}' not found.");
            return 1f; // Return zero if clip is null
        }
        // Default fallback length
    }

    //public void SwitchAnimationWithCrossfade(string animationName, float transitionDuration = 0.3f, float delay = 0f)
    //{
    //    StartCoroutine(SwitchAnimationCoroutineWithCrossfade(animationName, transitionDuration, delay));
    //}

    //IEnumerator SwitchAnimationCoroutineWithCrossfade(string animationName, float transitionDuration, float delay)
    //{
    //    yield return new WaitForSeconds(delay);

    //    anim.CrossFade(animationName, transitionDuration);

    //    // Wait for the crossfade to complete
    //    yield return new WaitForSeconds(transitionDuration);
    //}

}


//public class ArabAnimationController : MonoBehaviour
//{
//    public GameObject avatarCharacter;
//    public Animator anim;
//   //public float speed ;

//    /* public string animationName; */ // name of anim to play
//    void Start() {
//        anim = avatarCharacter.GetComponent<Animator>();


//    }

//    /* 
//        private IEnumerator MyAnimationCoroutine() {
//            anim.Play("آسف");
//            yield return new WaitForSeconds(2f);
//            anim.Play("أبدا");
//            yield return new WaitForSeconds(2f);

//        }

//        public void SwitchAnim() {
//            StartCoroutine(MyAnimationCoroutine());
//        } */

//    public void SwitchAnimation(string animationName, float delay = 0f)
//    {
//        StartCoroutine(SwitchAnimationCoroutine(animationName, delay));
//    }

//    //public void SwitchAnimation(string animationName, float delay = 0f)
//    //{
//    //    if (HasAnimation(animationName))
//    //    {
//    //        //anim.CrossFade(animationName, transitionDuration);
//    //        StartCoroutine(SwitchAnimationCoroutine(animationName, delay));
//    //    }
//    //    else
//    //    {
//    //        Debug.LogWarning("Animation not found: " + animationName);
//    //    }
//    //}

//    //public bool HasAnimation(string animationName)
//    //{
//    //    foreach (AnimationClip clip in anim.runtimeAnimatorController.animationClips)
//    //    {
//    //        if (clip.name == animationName)
//    //        {
//    //            return true;
//    //        }
//    //    }
//    //    return false;
//    //}

//    //private float GetCurrentAnimationLength()
//    //{
//    //    AnimatorStateInfo stateInfo = anim.GetCurrentAnimatorStateInfo(0);
//    //    Debug.Log("Info.length -> " + stateInfo.length);
//    //    Debug.Log("Info.length calculated-> " + stateInfo.length / 0.2f);
//    //    return stateInfo.length / 0.2f; //anim.speed;  // Adjust for speed
//    //}

//    private float GetCurrentAnimationLength(string animationName)
//    {
//        AnimatorStateInfo stateInfo = anim.GetCurrentAnimatorStateInfo(0);

//        // Verify if the animator is in the correct state
//        if (stateInfo.IsName(animationName))
//        {
//            Debug.Log("Info.length -> " + stateInfo.length);
//            Debug.Log("Info.length calculated-> " + stateInfo.length / anim.speed);
//            return stateInfo.length / anim.speed;  // Adjust for speed
//        }

//        return 0f;  // Default fallback if not in the correct state
//    }

//    IEnumerator SwitchAnimationCoroutine(string animationName, float delay) {
//       //yield return new WaitForSeconds(GetCurrentAnimationLength());
//        anim.speed = 1f;
//       anim.Play(animationName);
//        yield return new WaitForSeconds(0.1f);

//        float animationLength = GetCurrentAnimationLength(animationName);

//        // If the length is still infinity, we might need to handle it differently (fallback length or error)
//        if (float.IsInfinity(animationLength))
//        {
//            Debug.LogWarning("Animation length is Infinity. Fallback to a default value.");
//            animationLength = 3f; // Fallback to a default value
//        }
//        //animationLength = animationLength * 1000;
//        Debug.Log("Info.length final -> " + animationLength);

//        float speed = 3f / animationLength;
//        anim.speed = speed;
//        Debug.Log("Time speed -> " + speed);
//        Debug.Log("Time anim.speed -> " + anim.speed);
//        // Wait until the current animation finishes
//        yield return new WaitForSeconds(0.1f);
//    }

//}
