using UnityEngine;

namespace Version3
{
    public class OrbitAroundTarget : MonoBehaviour
    {
        // Reference to the target game object
        public Transform targetObject;

        // Camera settings
        public float distanceFromTarget = 5f; // Initial camera distance
        public float rotationSpeed = 0.5f; // Speed of rotation
        public float minHorizontalAngle = -90f; // Minimum horizontal angle (-90 degrees)
        public float maxHorizontalAngle = 90f; // Maximum horizontal angle (90 degrees)

        private Vector3 offset;
        private bool isRotating = false;

        void Start()
        {
            if (!targetObject)
                Debug.LogError("Target Object reference not assigned in inspector!");

            // Calculate initial offset
            offset = transform.position - targetObject.position;
            distanceFromTarget = offset.magnitude;
            offset = offset.normalized;

            // Ensure camera looks at the target object initially
            LookAtTarget();
        }

        void Update()
        {
            if (Input.GetMouseButtonDown(0))
            {
                isRotating = true;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                isRotating = false;
            }

            if (Input.touchCount > 0 && Input.touches[0].phase == TouchPhase.Moved)
            {
                isRotating = true;
            }
            else if (Input.touchCount == 0 || Input.touches[0].phase != TouchPhase.Moved)
            {
                isRotating = false;
            }

            if (isRotating)
            {
                OrbitCamera();
            }
        }

        void OrbitCamera()
        {
            float horizontalInput = Input.GetAxis("Mouse X") + Input.touches[0].deltaPosition.x;

            transform.RotateAround(targetObject.position, Vector3.up, horizontalInput * rotationSpeed);

            // Limit horizontal angle
            float angleY = Mathf.Repeat(transform.localEulerAngles.y + 360, 360);
            if (angleY > 180 && angleY < 360)
                angleY -= 360;
            transform.localRotation = Quaternion.Euler(
                transform.localEulerAngles.x,
                Mathf.Clamp(angleY, minHorizontalAngle, maxHorizontalAngle),
                transform.localEulerAngles.z);

            // Maintain distance and look at target
            offset = (transform.position - targetObject.position).normalized;
            transform.position = targetObject.position + offset * distanceFromTarget;
            LookAtTarget();
        }

        void LookAtTarget()
        {
            transform.LookAt(targetObject);
        }
    }
}
