using System.Collections;
using System.Collections.Generic;
using FlutterUnityIntegration;
using UnityEngine;
using Newtonsoft.Json;
using System;

public class ArabicSignAnim : MonoBehaviour
{
    public ArabAnimationController animController;
    private Queue<(string animation, string word, string screen)> animationQueue = new Queue<(string animation, string word, string screen)>();
    private bool isPlaying = false;
    private Coroutine currentAnimationCoroutine;

    void Start()
    {
        animController.anim.speed = 1f; // Set initial speed
    }

    public void PlaySignAnim(string message)
    {
        Debug.Log("Inside PlaySignAnim: " + message);
        var dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(message);

        if (dict.TryGetValue("root", out string animation) && dict.TryGetValue("word", out string animationWord) && dict.TryGetValue("screen", out string animationScreen))
        {
            animationQueue.Enqueue((animation, animationWord, animationScreen));

            if (!isPlaying)
            {
                StartAnimationQueue();
            }
        }
        else
        {
            Debug.LogError("Required keys 'root' or 'word' are missing from the message.");
        }
    }

    public void SetAnimationSpeed(string message)
    {
        animController.AdjustAnimationSpeed(message);
    }

    private void StartAnimationQueue()
    {
        if (!isPlaying)
        {
            isPlaying = true;
            currentAnimationCoroutine = StartCoroutine(PlayAnimationQueue());
        }
    }

    private IEnumerator PlayAnimationQueue()
    {
        isPlaying = true;

        while (animationQueue.Count > 0)
        {
            var current = animationQueue.Dequeue();
            UnityMessageManager.Instance.SendMessageToFlutter("Current playing Animation => " + current.word + " &" + "screen=>" + current.screen);

            float animationLength = GetAnimationLength(current.animation);
            Debug.Log($"Playing animation: {current.animation}, Length: {animationLength}");

            animController.SwitchAnimation(current.animation, 0f); // Start animation immediately

            yield return new WaitForSeconds(animationLength); // Wait for the entire animation duration
            UnityMessageManager.Instance.SendMessageToFlutter("Current Animation => " + current.animation);


            // Optional: Add a small delay between animations
            yield return new WaitForSeconds(0f);
        }

        SwitchToIdle();
        isPlaying = false;
    }

    public void StopAnimations()
    {
        Debug.Log("Stopping animations...");
        isPlaying = false;
        if (currentAnimationCoroutine != null)
        {
            StopCoroutine(currentAnimationCoroutine);
        }
        ClearAnimationQueue();
        SwitchToIdle();
    }

    private void ClearAnimationQueue()
    {
        animationQueue.Clear();
        Debug.Log("Animation queue cleared.");
    }

    float GetAnimationLength(string animationName)
    {
        AnimationClip[] clips = animController.anim.runtimeAnimatorController.animationClips;

        AnimationClip clip = Array.Find(clips, c => c.name == animationName);

        if (clip != null)
        {
            return clip.length / animController.anim.speed;
        }
        return 0.5f; // Default fallback length
    }

    void SwitchToIdle()
    {
        animController.SwitchAnimation("Idle");
        UnityMessageManager.Instance.SendMessageToFlutter("switchtoIdle");
    }
}




//using System.Collections;
//using System.Collections.Generic;
//using FlutterUnityIntegration;
//using UnityEngine;
//using Newtonsoft.Json;
//using System;

//public class ArabicSignAnim : MonoBehaviour
//{
//    public ArabAnimationController animController;
//    private Queue<(string animation, string word, string screen)> animationQueue = new Queue<(string animation, string word, string screen)>();
//    private bool isPlaying = false;

//    void Start()
//    {
//        animController.anim.speed = 1f; // Set initial speed
//    }

//    public void PlaySignAnim(string message)
//    {
//        Debug.Log("Inside PlaySignAnim: " + message);
//        var dict = JsonConvert.DeserializeObject<Dictionary<string, string>>(message);

//        if (dict.TryGetValue("root", out string animation) && dict.TryGetValue("word", out string animationWord) && dict.TryGetValue("screen", out string animationScreen))
//        {
//            animationQueue.Enqueue((animation, animationWord, animationScreen));

//            if (!isPlaying)
//            {
//                StartCoroutine(PlayAnimationQueue());
//            }
//        }
//        else
//        {
//            Debug.LogError("Required keys 'root' or 'word' are missing from the message.");
//        }
//    }

//    private IEnumerator PlayAnimationQueue()
//    {
//        isPlaying = true;

//        while (animationQueue.Count > 0)
//        {
//            var current = animationQueue.Dequeue();
//            UnityMessageManager.Instance.SendMessageToFlutter("Current playing Animation => " + current.word + " &" + "screen=>" + current.screen);

//            float animationLength = GetAnimationLength(current.animation);
//            Debug.Log($"Playing animation: {current.animation}, Length: {animationLength}");

//            animController.SwitchAnimation(current.animation, 0f); // Start animation immediately

//            yield return new WaitForSeconds(animationLength); // Wait for the entire animation duration

//            // Optional: Add a small delay between animations
//            yield return new WaitForSeconds(0.5f);
//        }

//        SwitchToIdle();
//        isPlaying = false;
//    }

//    float GetAnimationLength(string animationName)
//    {
//        AnimationClip[] clips = animController.anim.runtimeAnimatorController.animationClips;

//        AnimationClip clip = Array.Find(clips, c => c.name == animationName);

//        if (clip != null)
//        {
//            return clip.length / animController.anim.speed;
//        }
//        return 3f; // Default fallback length
//    }


//    void SwitchToIdle()
//    {
//        animController.SwitchAnimation("Idle");
//    }
//}
