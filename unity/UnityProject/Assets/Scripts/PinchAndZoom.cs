using UnityEngine;

namespace Version3
{
    public class PinchAndZoom : MonoBehaviour
    {
        // Field of View limits
        public static float MinFOV = 70f; // Maximum zoom in
        public static float MaxFOV = 120f; // Maximum zoom out
        private bool isZooming = false;
        private float currentRotation = 0f; // Variable to track current rotation
        public Transform avatar; // Reference to the avatar to revolve around
        public float currentRotationAngleY = 0f; // For horizontal rotation
        public float currentRotationAngleX = 0f; // For vertical rotation

        public static PinchZoom HANDLE;

        [SerializeField] private Camera targetCamera;

        // Vertical rotation limits (for up and down camera rotation)
        private const float MinVerticalAngle = -20f;
        private const float MaxVerticalAngle = 20f;

        void Awake()
        {
            if (targetCamera != null)
            {
                targetCamera.orthographic = false;
                SetInitialFOV();
            }
        }

        void Start()
        {
            SetInitialFOV();
        }

        void SetInitialFOV()
        {
            if (targetCamera != null)
            {
                targetCamera.fieldOfView = Mathf.Clamp(90f, MinFOV, MaxFOV);
            }
        }

        public void ChangeCamera(Camera newTargetCamera)
        {
            float newInitialFOV = newTargetCamera.fieldOfView;
            newTargetCamera.fieldOfView = targetCamera.fieldOfView;
            targetCamera.fieldOfView = newInitialFOV;
        }

        public float panSpeedAdjustment = 1.0f;

        public float currentFOV = 0f;

        public float lastDistance = 0f;

        void Update()
        {
            if (Input.touchCount >= 2)
            {
                isZooming = true;
                Vector2 touch0, touch1;
                float distance;
                touch0 = Input.GetTouch(0).position;
                touch1 = Input.GetTouch(1).position;
                distance = Vector2.Distance(touch0, touch1);
                if (lastDistance == 0f)
                {
                    lastDistance = distance;
                    currentFOV = targetCamera.fieldOfView;
                }
                else
                {
                    if (distance > lastDistance)
                    {
                        // Zoom out
                        float multiplier = lastDistance / distance;
                        float newFOV = currentFOV * multiplier;
                        newFOV = Mathf.Clamp(newFOV, MinFOV, MaxFOV); // Clamp FOV within limits
                        targetCamera.fieldOfView = newFOV;
                        AdjustPanSpeed(newFOV);
                    }
                    else
                    {
                        // Zoom in
                        float multiplier = lastDistance / distance;
                        float newFOV = currentFOV * multiplier;
                        newFOV = Mathf.Clamp(newFOV, MinFOV, MaxFOV); // Clamp FOV within limits
                        targetCamera.fieldOfView = newFOV;
                        AdjustPanSpeed(newFOV);
                    }
                }
            }
            else if (Input.touchCount == 1 && Input.GetTouch(0).phase == TouchPhase.Moved)
            {
                // Handle horizontal and vertical swipe for rotation
                Vector2 swipeDelta = Input.GetTouch(0).deltaPosition;

                // Rotate camera horizontally (left-right) and vertically (up-down)
                RotateCamera(swipeDelta.x, swipeDelta.y);
            }
            else
            {
                isZooming = false;
                lastDistance = 0f;
                currentFOV = 0f;
                AdjustPanSpeed(targetCamera.fieldOfView);
            }

            Vector3 cameraPosition = targetCamera.transform.position;
            // Debug.Log("Camera Position: " + cameraPosition);
        }

        public bool IsZooming()
        {
            return isZooming;
        }

        public void AdjustPanSpeed(float fieldOfView)
        {
            panSpeedAdjustment = (fieldOfView - MinFOV) / (MaxFOV - MinFOV); // scales from 0 to 1
        }

        // Modified RotateCamera method to include vertical rotation
        void RotateCamera(float horizontalSwipe, float verticalSwipe)
        {
            const float rotationSpeed = 10f; // Adjust speed to your preference

            // Horizontal rotation (around Y-axis)
            float horizontalRotationAngle = horizontalSwipe * Time.deltaTime * rotationSpeed;
            float newRotationAngleY = currentRotationAngleY + horizontalRotationAngle;

            // Clamp horizontal rotation to -75 and 75 degrees
            if (newRotationAngleY <= 75f && newRotationAngleY >= -75f)
            {
                currentRotationAngleY = newRotationAngleY;
                targetCamera.transform.RotateAround(avatar.position, Vector3.up, horizontalRotationAngle); // Rotate horizontally
            }

            float verticalRotationAngle = -verticalSwipe * Time.deltaTime * rotationSpeed; // Negative to invert swipe direction
            float newRotationAngleX = currentRotationAngleX + verticalRotationAngle;

            // Clamp vertical rotation to MinVerticalAngle and MaxVerticalAngle (e.g., -70 and 70 degrees)
            newRotationAngleX = Mathf.Clamp(newRotationAngleX, MinVerticalAngle, MaxVerticalAngle);

            // Apply vertical rotation
            currentRotationAngleX = newRotationAngleX;

            // Update camera's rotation by setting Euler angles directly
            targetCamera.transform.rotation = Quaternion.Euler(currentRotationAngleX, currentRotationAngleY, 0f);

            // Optionally log the current rotation angles for debugging
            Debug.Log("Horizontal Rotation: " + currentRotationAngleY + " | Vertical Rotation: " + currentRotationAngleX + "" + targetCamera.transform.position);
        }
    }
}
