using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PinchZoom : MonoBehaviour
{
    private static readonly float PanSpeed = 20f;
    private static readonly float ZoomSpeedTouch = 0.1f;
    private static readonly float ZoomSpeedMouse = 0.5f;
    private static readonly float RotationSpeed = 100f;
    private static readonly float ZoomMovementSpeed = 5f;

    private static readonly float[] BoundsX = new float[] { -10f, 5f };
    private static readonly float[] BoundsZ = new float[] { -18f, -4f };
    private static readonly float[] ZoomBounds = new float[] { 10f, 85f };
    private static readonly float[] ZoomMovementBounds = new float[] { 5f, 50f };

    private Camera cam;

    private Vector3 lastPanPosition;
    private int panFingerId; // Touch mode only

    private bool wasZoomingLastFrame; // Touch mode only
    private Vector2[] lastZoomPositions; // Touch mode only

    void Awake()
    {
        cam = GetComponent<Camera>();
    }

    void Update()
    {
        if (Input.touchSupported && Application.platform != RuntimePlatform.WebGLPlayer)
        {
            HandleTouch();
        }
        else
        {
            HandleMouse();
        }
    }

    void HandleTouch()
    {
        switch (Input.touchCount)
        {
            case 1: // Panning
                wasZoomingLastFrame = false;

                Touch touch = Input.GetTouch(0);
                if (touch.phase == TouchPhase.Began)
                {
                    lastPanPosition = touch.position;
                    panFingerId = touch.fingerId;
                }
                else if (touch.fingerId == panFingerId && touch.phase == TouchPhase.Moved)
                {
                    PanCamera(touch.position);
                }
                break;

            case 2: // Zooming and Rotation
                Vector2[] newPositions = new Vector2[] { Input.GetTouch(0).position, Input.GetTouch(1).position };
                if (!wasZoomingLastFrame)
                {
                    lastZoomPositions = newPositions;
                    wasZoomingLastFrame = true;
                }
                else
                {
                    // Calculate distance between touches
                    float newDistance = Vector2.Distance(newPositions[0], newPositions[1]);
                    float oldDistance = Vector2.Distance(lastZoomPositions[0], lastZoomPositions[1]);
                    float offset = newDistance - oldDistance;

                    // Calculate rotation angle
                    Vector2 oldCenter = (lastZoomPositions[0] + lastZoomPositions[1]) / 2;
                    Vector2 newCenter = (newPositions[0] + newPositions[1]) / 2;
                    float angle = Vector2.SignedAngle(oldCenter - newCenter, Vector2.right);

                    ZoomCamera(offset, ZoomSpeedTouch);
                    RotateCamera(angle * Time.deltaTime);

                    lastZoomPositions = newPositions;
                }
                break;

            default:
                wasZoomingLastFrame = false;
                break;
        }
    }

    void HandleMouse()
    {
        // On mouse down, capture it's position.
        // Otherwise, if the mouse is still down, pan the camera.
        if (Input.GetMouseButtonDown(0))
        {
            lastPanPosition = Input.mousePosition;
        }
        else if (Input.GetMouseButton(0))
        {
            PanCamera(Input.mousePosition);
        }

        // Check for scrolling to zoom the camera
        float scrollX = Input.GetAxis("Mouse ScrollWheel Horizontal");
        float scrollY = Input.GetAxis("Mouse ScrollWheel");

        if (Mathf.Abs(scrollX) > 0)
        {
            RotateCamera(scrollX * RotationSpeed * Time.deltaTime);
        }

        if (Mathf.Abs(scrollY) > 0)
        {
            ZoomCamera(scrollY, ZoomSpeedMouse);
        }
    }

    void PanCamera(Vector3 newPanPosition)
    {
        Vector3 offset = cam.ScreenToViewportPoint(lastPanPosition - newPanPosition);
        Vector3 move = new Vector3(offset.x * PanSpeed, 0, offset.y * PanSpeed);

        transform.Translate(move, Space.World);

        Vector3 pos = transform.position;
        pos.x = Mathf.Clamp(transform.position.x, BoundsX[0], BoundsX[1]);
        pos.z = Mathf.Clamp(transform.position.z, BoundsZ[0], BoundsZ[1]);
        transform.position = pos;

        lastPanPosition = newPanPosition;
    }

    void ZoomCamera(float offset, float speed)
    {
        if (offset == 0)
        {
            return;
        }

        cam.fieldOfView = Mathf.Clamp(cam.fieldOfView - (offset * speed), ZoomBounds[0], ZoomBounds[1]);

        Vector3 movement = cam.transform.forward * (offset * ZoomMovementSpeed);
        cam.transform.Translate(movement, Space.World);

        Vector3 pos = cam.transform.position;
        pos.z = Mathf.Clamp(pos.z, ZoomMovementBounds[0], ZoomMovementBounds[1]);
        cam.transform.position = pos;
    }

    void RotateCamera(float angle)
    {
        transform.Rotate(Vector3.up, angle, Space.Self);
    }
}
