Library: /Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/unity/UnityProject/Temp/BurstOutput/tempburstlibs/arm64-v8a/lib_burst_generated
--platform=Android
--backend=burst-llvm-16
--target=ARMV8A_AARCH64
--global-safety-checks-setting=Off
--meta-data-generation=False
--dump=Function
--float-precision=Standard
--target-framework=NetFramework
--assembly-defines=Assembly-CSharp;
--assembly-defines=Mono.Security;
--assembly-defines=mscorlib;
--assembly-defines=Newtonsoft.Json;
--assembly-defines=System.Configuration;
--assembly-defines=System.Core;
--assembly-defines=System;
--assembly-defines=System.Numerics;
--assembly-defines=System.Runtime.Serialization;
--assembly-defines=System.Xml;
--assembly-defines=System.Xml.Linq;
--assembly-defines=Unity.InputSystem;
--assembly-defines=Unity.InputSystem.ForUI;
--assembly-defines=Unity.TextMeshPro;
--assembly-defines=UnityEngine;
--generate-link-xml=Temp/burst.link.xml
--temp-folder=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/unity/UnityProject/Temp/Burst
--key-folder=/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer
--decode-folder=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/unity/UnityProject/Library/Burst
--output=/Users/<USER>/Documents/gitlab asl/ASL-14082024/asl-flutter-app/unity/UnityProject/Temp/BurstOutput/tempburstlibs/arm64-v8a/lib_burst_generated
--pdb-search-paths=Temp/ManagedSymbols/


