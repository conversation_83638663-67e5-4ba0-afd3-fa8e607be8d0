-target:library
-out:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll"
-refout:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ref.dll"
-define:UNITY_2022_3_21
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_OSX
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UNITY_INPUT_SYSTEM_ENABLE_VR
-define:UNITY_INPUT_SYSTEM_ENABLE_XR
-define:UNITY_INPUT_SYSTEM_ENABLE_PHYSICS
-define:UNITY_INPUT_SYSTEM_ENABLE_PHYSICS2D
-define:UNITY_INPUT_SYSTEM_ENABLE_UI
-define:HAS_SET_LOCAL_POSITION_AND_ROTATION
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll"
-r:"Assets/FlutterUnityIntegration/JsonDotNet/Assemblies/AOT/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.3.1/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.3.1/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.3.1/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@2.3.1/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@1.2.5/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.1/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.1/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.1/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.1/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.ref.dll"
-analyzer:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/AxisComposite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/ButtonWithOneModifier.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/ButtonWithTwoModifiers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/OneModifierComposite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/TwoModifiersComposite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/Vector2Composite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Composites/Vector3Composite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/IInputActionCollection.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/IInputInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputAction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionAsset.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionChange.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionMap.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionParameters.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionPhase.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionProperty.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionRebindingExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionReference.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionSetupExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionTrace.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputActionType.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputBinding.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputBindingComposite.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputBindingCompositeContext.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputBindingResolver.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputControlScheme.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/InputInteractionContext.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Interactions/HoldInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Interactions/MultiTapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Interactions/PressInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Interactions/SlowTapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Actions/Interactions/TapInteraction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/AssemblyInfo.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/AnyKeyControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/AxisControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/ButtonControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/CommonUsages.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/DeltaControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/DiscreteButtonControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/DoubleControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/DpadControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlLayout.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlLayoutAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlLayoutChange.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlList.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputControlPath.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/InputProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/IntegerControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/KeyControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/AxisDeadzoneProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/ClampProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/CompensateDirectionProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/CompensateRotationProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/EditorWindowSpaceProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/InvertProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/InvertVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/InvertVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/NormalizeProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/NormalizeVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/NormalizeVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/ScaleProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/ScaleVector2Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/ScaleVector3Processor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Processors/StickDeadzoneProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/QuaternionControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/StickControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/TouchControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/TouchPhaseControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/TouchPressControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Vector2Control.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Controls/Vector3Control.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/DisableDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/EnableDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/EnableIMECompositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/IInputDeviceCommandInfo.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/InitiateUserAccountPairingCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/InputDeviceCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryCanRunInBackground.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryDimensionsCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryEditorWindowCoordinatesCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryEnabledStateCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryKeyboardLayoutCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryKeyNameCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryPairedUserAccountCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QuerySamplingFrequencyCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/QueryUserIdCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/RequestResetCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/RequestSyncCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/SetIMECursorPositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/SetSamplingFrequencyCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/UseWindowsGamingInputCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Commands/WarpMousePositionCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Gamepad.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Haptics/DualMotorRumble.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Haptics/DualMotorRumbleCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Haptics/IDualMotorRumble.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Haptics/IHaptics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/ICustomDeviceReset.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/IEventMerger.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/IEventPreProcessor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/IInputUpdateCallbackReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/InputDevice.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/InputDeviceBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/InputDeviceChange.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/InputDeviceDescription.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/InputDeviceMatcher.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/ITextInputReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Joystick.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Keyboard.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Mouse.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Pen.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Pointer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Precompiled/FastKeyboard.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Precompiled/FastMouse.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Precompiled/FastMouse.partial.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Precompiled/FastTouchscreen.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Remote/InputRemoting.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Remote/RemoteInputPlayerConnection.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Sensor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/Touchscreen.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Devices/TrackedDevice.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionAssetManager.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionEditorToolbar.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionEditorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputActionTreeViewItems.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/InputBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/NameAndParameterListView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/ParameterListView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetEditor/PropertiesViewBase.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetImporter/InputActionAssetEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetImporter/InputActionCodeGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetImporter/InputActionImporter.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/AssetImporter/InputActionImporterEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/BuildPipeline/LinkFileGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/IInputControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/InputControlDropdownItem.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/InputControlPathEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/InputControlPicker.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/InputControlPickerDropdown.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/InputControlPickerState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/Layouts/DefaultInputControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/ControlPicker/Layouts/TouchscreenControlPickerLayout.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Debugger/InputActionDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Debugger/InputDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Debugger/InputDeviceDebuggerWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/DeviceSimulator/InputSystemPlugin.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/DownloadableSample.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/EditorInputControlLayoutCache.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/InputDiagnostics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/InputLayoutCodeGenerator.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/InputParameterEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdown.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownGUI.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownItem.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/AdvancedDropdownWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/CallbackDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/AdvancedDropdown/MultiLevelDataSource.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/EditorHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/GUIHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/InputActionSerializationHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/InputControlTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/InputEventTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/InputStateWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/SerializedPropertyHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/SerializedPropertyLinqExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Internal/TreeViewHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/PropertyDrawers/InputActionDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/PropertyDrawers/InputActionDrawerBase.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/PropertyDrawers/InputActionMapDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/PropertyDrawers/InputActionPropertyDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/PropertyDrawers/InputControlPathDrawer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Settings/EditorPlayerSettingHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Settings/InputEditorUserSettings.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Settings/InputSettingsBuildProvider.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/Settings/InputSettingsProvider.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Commands/Commands.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Commands/ControlSchemeCommands.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/EnumerableExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/ExpressionUtils.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/InputActionsEditorConstants.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/InputActionsEditorSettingsProvider.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/InputActionsEditorState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/InputActionsEditorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/ReactiveProperty.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/SerializedInputAction.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/SerializedInputActionMap.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/SerializedInputBinding.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/StateContainer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ActionMapsView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ActionPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ActionsTreeView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/BindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/CompositeBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/CompositePartBindingPropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ContextMenu.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ControlSchemesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/InputActionsEditorView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/InputActionsTreeViewItem.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/InputActionViewsControlsHolder.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/IViewStateCollection.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/NameAndParametersListView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/PropertiesView.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/Selectors.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ViewBase.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/ViewStateCollection.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Editor/UITKAssetEditor/Views/VisualElementExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/ActionEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/DeltaStateEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/DeviceConfigurationEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/DeviceRemoveEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/DeviceResetEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/IInputEventTypeInfo.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/IMECompositionEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEventBuffer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEventListener.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEventPtr.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEventStream.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/InputEventTrace.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/StateEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Events/TextEvent.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/IInputDiagnostics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/IInputRuntime.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputAnalytics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputExtensions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputFeatureNames.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputManager.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputManagerStateMonitors.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputMetrics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputSettings.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputSystem.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputSystemObject.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/InputUpdateType.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/NativeInputRuntime.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Android/AndroidAxis.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Android/AndroidGameController.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Android/AndroidKeyCode.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Android/AndroidSensors.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Android/AndroidSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/DualShock/DualShockGamepad.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/DualShock/DualShockGamepadHID.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/DualShock/DualShockSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/DualShock/IDualShockHaptics.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/EnhancedTouch/EnhancedTouchSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/EnhancedTouch/Finger.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/EnhancedTouch/Touch.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/EnhancedTouch/TouchHistory.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/EnhancedTouch/TouchSimulation.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/HID/HID.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/HID/HIDDescriptorWindow.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/HID/HIDParser.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/HID/HIDSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/InputSettingsiOS.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/InputSettingsiOSProvider.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/IOSGameController.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/iOSPostProcessBuild.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/iOSStepCounter.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/iOS/iOSSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Linux/LinuxSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Linux/SDLDeviceBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OnScreen/OnScreenButton.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OnScreen/OnScreenControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OnScreen/OnScreenStick.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OnScreen/OnScreenSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OSX/OSXGameController.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/OSX/OSXSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/DefaultInputActions.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/InputValue.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerInput.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerInputEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerInputManager.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerInputManagerEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerJoinBehavior.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/PlayerInput/PlayerNotifications.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/IStreamControllerAPI.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/SteamController.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/SteamControllerType.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/SteamHandle.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/SteamIGAConverter.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Steam/SteamSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Switch/SwitchProControllerHID.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Switch/SwitchSupportHID.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/BaseInputOverride.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/ExtendedAxisEventData.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/ExtendedPointerEventData.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/InputSystemUIInputModule.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/InputSystemUIInputModuleEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/MultiplayerEventSystem.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/NavigationModel.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/PointerModel.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/StandaloneInputModuleEditor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/TrackedDeviceRaycaster.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/UISupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UI/VirtualMouseInput.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/UnityRemote/UnityRemoteSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/Users/<USER>"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/WebGL/WebGLGamepad.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/WebGL/WebGLJoystick.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/WebGL/WebGLSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XInput/IXboxOneRumble.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XInput/XboxGamepadMacOS.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XInput/XInputController.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XInput/XInputControllerWindows.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XInput/XInputSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Controls/PoseControl.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Devices/GoogleVR.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Devices/Oculus.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Devices/OpenVR.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Devices/WindowsMR.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/GenericXRDevice.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Haptics/BufferedRumble.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Haptics/GetCurrentHapticStateCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Haptics/GetHapticCapabilitiesCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Haptics/SendBufferedHapticsCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/Haptics/SendHapticImpulseCommand.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/TrackedPoseDriver.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/XRLayoutBuilder.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Plugins/XR/XRSupport.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/IInputStateCallbackReceiver.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/IInputStateChangeMonitor.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/IInputStateTypeInfo.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/InputState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/InputStateBlock.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/InputStateBuffers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/State/InputStateHistory.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/ArrayHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/CallbackArray.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Comparers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/CSharpCodeHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/DelegateHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/DisplayStringFormatAttribute.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/DynamicBitfield.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/ExceptionHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/FourCC.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/InlinedArray.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/InternedString.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/JsonParser.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/MemoryHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/MiscHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/NameAndParameters.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/NamedValue.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/NumberHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/ForDeviceEventObservable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/Observable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/Observer.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/SelectManyObservable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/SelectObservable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/TakeNObservable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Observables/WhereObservable.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/OneOrMore.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/PredictiveParser.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/PrimitiveValue.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/ReadOnlyArray.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/SavedState.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/SpriteUtilities.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/StringHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/Substring.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/TypeHelpers.cs"
"Library/PackageCache/com.unity.inputsystem@1.7.0/InputSystem/Utilities/TypeTable.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319

/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US

-warn:0

/additionalfile:"Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"