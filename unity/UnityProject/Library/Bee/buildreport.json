{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 19377, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "Unity"}}, {"pid": 19377, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-100"}}, {"pid": 19377, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 19377, "tid": 1, "ts": 1753879146828153, "dur": 236469, "ph": "X", "name": "Build player", "args": {}}, {"pid": 19377, "tid": 1, "ts": 1753879146831462, "dur": 25477, "ph": "X", "name": "Preprocess Player", "args": {}}, {"pid": 19377, "tid": 1, "ts": 1753879147069721, "dur": 804, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 19377, "tid": 1, "ts": 1753879147074011, "dur": 558, "ph": "X", "name": "", "args": {}}, {"pid": 19377, "tid": 1, "ts": 1753879147071934, "dur": 4463, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}