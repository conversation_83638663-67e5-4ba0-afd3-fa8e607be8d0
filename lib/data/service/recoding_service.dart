import 'package:arabic_sign_language/data/models/recording_model/recording_model.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../../presentation/core/url.dart';

class RecordingService {
  final Dio dio;
  RecordingService({Dio? dio})
      : dio = dio ??
            Dio(BaseOptions(
              baseUrl: baseUrl,
              headers: {"Content-Type": "multipart/form-data"},
            ));
  // final dio = Dio(BaseOptions(
  //   baseUrl: baseUrl,
  //   headers: {"Content-Type": "multipart/form-data"},
  // ));

  Future<List<RecordingModel>> getTranscriptionFromRecording(
      Uint8List recording) async {
    List<RecordingModel> recordingData = [];
    try {
      final formData = FormData.fromMap({
        'file': MultipartFile.fromBytes(recording, filename: 'recording.WAV')
      });
      final response = await dio.post(uploadVoice, data: formData);
      print("response => getTranscriptionFromRecording => ${response.data}");
      if (response.statusCode == 200 && response.data['status']) {
        final List<dynamic> data = response.data['data'];
        print("recordings => getTranscriptionFromRecording => $data");
        recordingData = data.map((e) => RecordingModel.fromJson(e)).toList();
        print("recordings => getTranscriptionFromRecording => $recordingData");
      }
      return recordingData;
    } catch (e) {
      print("error => getTranscriptionFromRecording =>$e");
      return recordingData;
    }
  }
}
