import 'package:arabic_sign_language/data/models/text_transcription_model/text_transcription_model.dart';
import 'package:dio/dio.dart';

import '../../presentation/core/url.dart';

class TextConversionService {
  final Dio dio;
  TextConversionService({Dio? dio})
      : dio = dio ?? Dio(BaseOptions(baseUrl: baseUrl));

  Future<List<TextTranscriptionModel>> getTranscribedText(String text) async {
    List<TextTranscriptionModel> transcriptionList = [];
    try {
      final response = await dio.post(
        convertText,
        data: {
          "arabic_text": text,
        },
      );
      print("response => getTranscribedText => ${response.data}");
      if (response.statusCode == 200 && response.data['status']) {
        final List<dynamic> data = response.data['data'];
        print("response => getTranscribedText => $data");
        transcriptionList =
            data.map((i) => TextTranscriptionModel.fromJson(i)).toList();
      }
      return transcriptionList;
    } catch (e) {
      print("error => getTranscriptionFromRecording =>$e");
      return transcriptionList;
    }
  }
}
