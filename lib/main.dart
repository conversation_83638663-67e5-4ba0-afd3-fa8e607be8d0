import 'package:arabic_sign_language/bloc/UnityScreen/unity_screen_bloc.dart';
import 'package:arabic_sign_language/bloc/connectionChecker/connection_checker_bloc.dart';
import 'package:arabic_sign_language/bloc/youtubeScreen/youtube_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';
import 'package:arabic_sign_language/firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'presentation/screens/splash/splash_screen.dart';
import 'package:flutter/material.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider<UnityScreenBloc>(
        create: (context) => UnityScreenBloc(
          RecordingService(),
          TextConversionService(),
          TranscriptService(),
        ),
      ),
      BlocProvider<YoutubeScreenBloc>(
        create: (context) => YoutubeScreenBloc(
          TranscriptService(),
        ),
      ),
      BlocProvider(
        create: (context) =>
            ConnectionCheckerBloc()..add(ConnectivityObserver()),
      )
    ],
    child: const MyApp(),
  ));
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'ASL',
      theme: ThemeData(
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            backgroundColor: Color(0xFF0068F8),
          ),
          scaffoldBackgroundColor: Color(0xFFE7E7E7),
          bottomNavigationBarTheme: const BottomNavigationBarThemeData(
            selectedItemColor: Color(0xFF1E56B1),
            backgroundColor: Colors.white,
          )),
      home: const SplashScreen(),
      builder: (context, child) {
        final size = MediaQuery.sizeOf(context);
        return BlocListener<ConnectionCheckerBloc, ConnectionCheckerState>(
          listener: (context, state) {
            if (state is ConnectivityStatusChanged) {
              final isConnected = state.isNetworkConnected;
              final snackBar = SnackBar(
                content: Text(
                  isConnected
                      ? "Connected to the internet"
                      : "No internet connection",
                ),
                backgroundColor: isConnected ? Colors.green : Colors.red,
                behavior: SnackBarBehavior.floating,
                duration: Duration(
                  seconds: isConnected ? 3 : 1800,
                ),
                margin: EdgeInsets.only(
                  bottom: size.height * 0.05,
                  right: 10,
                  left: 10,
                ),
              );

              // Show the snackbar using ScaffoldMessenger
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              ScaffoldMessenger.of(context).showSnackBar(snackBar);
            }
          },
          child: child,
        );
      },
      navigatorKey: navigatorKey,
    );
  }
}
