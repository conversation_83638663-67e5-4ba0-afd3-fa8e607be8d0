import 'package:arabic_sign_language/presentation/core/themes/text_theme.dart';
import 'package:flutter/material.dart';

//-----------  default character ----------------//
bool isDefaultCharacterSelected = true;

//--------------- Assets ---------------------//

const splashIcon = "assets/images/splash.png";
const notificationIcon = "assets/images/notification.png";

//---------Onboarding------------
const background = Color.fromARGB(255, 35, 35, 35);
// const background = Color.fromRGBO(52, 55, 80, 1);
const pageImageColor = Color.fromARGB(255, 212, 212, 212);
const pageTitleStyle = TextStyle(
  fontSize: 20.0,
  wordSpacing: 1,
  letterSpacing: 1.2,
  fontWeight: FontWeight.w900,
  color: Colors.black,
);
const pageInfoStyle = TextStyle(
  color: Colors.black,
  letterSpacing: 0.8,
  height: 1.6,
  fontSize: 14,
);

//---------Skip button-----------

const defaultSkipButtonColor = Color.fromARGB(255, 93, 93, 93);
const defaultSkipButtonBorderRadius = BorderRadius.all(Radius.circular(20.0));
const defaultSkipButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const defaultSkipButtonTextStyle =
    TextStyle(color: Colors.white, letterSpacing: 1.0);

//---------Signin button-----------

const signinButtonColor = Color.fromARGB(255, 158, 69, 69);
const signinButtonBorderRadius = BorderRadius.all(Radius.circular(20.0));
const signinButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const signinButtonTextStyle =
    TextStyle(color: Colors.white, letterSpacing: 1.0);

//--------Proceed Button---------

const defaultProceedButtonColor = Color.fromARGB(255, 88, 94, 147);
const defaultProceedButtonBorderRadius =
    BorderRadius.all(Radius.circular(20.0));
const defaultProceedButtonPadding =
    EdgeInsets.symmetric(horizontal: 17.0, vertical: 5.0);
const defaultProceedButtonTextStyle = TextStyle(
  color: Colors.white,
  letterSpacing: 1.0,
);

const appBg = Color(0xFF0068F8);
const buttonColor = Color(0xFF1E56B1);
const guestButtonColor = Color(0xFF289900);
final disabledButtonColor = Color(0xFF054DA4).withOpacity(0.4);
final overlayColor = Color(0xFF1D1E20).withOpacity(0.9);

//------------------ Fonts -------------------
const String FONT_FAMILY = 'Poppins';
const String FONT_FAMILY_INTER = 'Inter';

Widget onBoardingItem(
    {required String image,
    required String title,
    required String description}) {
  return Container(
    color: appBg,
    child: Column(
      children: [
        const SizedBox(height: 50),
        Text(
          "Welcome to",
          style: AppTextTheme.mediumBoldText(fontSize: 20),
        ),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                  text: "Arabic ",
                  style: AppTextTheme.mediumBoldText(fontSize: 18)),
              TextSpan(text: "Sign Language", style: AppTextTheme.smallText())
            ],
          ),
        ),
        const SizedBox(height: 15),
        Image.asset(
          "assets/images/$image.png",
          height: 300,
        ),
        Expanded(
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(45),
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Text(
                    title,
                    style: AppTextTheme.onBoardingText(
                        fontSize: 20,
                        fontColor: const Color(0xFF343434),
                        fontWeight: FontWeight.w600),
                    textAlign: TextAlign.left,
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
                  child: Text(
                    description,
                    style: AppTextTheme.onBoardingText(
                        fontSize: 14,
                        fontColor: const Color(0xFF666666),
                        fontWeight: FontWeight.w400),
                    textAlign: TextAlign.left,
                  ),
                ),
              ],
            ),
          ),
        )
      ],
    ),
  );
}

Widget imageItem(
    {required String image,
    double top = 0,
    double left = 0,
    double right = 0}) {
  return Positioned(
    top: top,
    left: left,
    right: right,
    child: Container(
      width: 120,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Color(0xFFC8C8C8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Image.asset(
          'assets/images/$image.png',
          width: 150,
          height: 160,
          // fit: BoxFit.fill,
        ),
      ),
    ),
  );
}

Widget onBoardingStackItem({required String title}) {
  return Container(
    color: appBg,
    child: Stack(
      fit: StackFit.passthrough,
      children: [
        Column(
          children: [
            const SizedBox(height: 50),
            Text(
              "Welcome to",
              style: AppTextTheme.mediumBoldText(fontSize: 20),
            ),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                      text: "Arabic ",
                      style: AppTextTheme.mediumBoldText(fontSize: 18)),
                  TextSpan(
                      text: "Sign Language", style: AppTextTheme.smallText())
                ],
              ),
            ),
            const SizedBox(height: 15),
            Spacer(),
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(45),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 10),
                      child: Text(
                        title,
                        style: AppTextTheme.onBoardingText(
                            fontSize: 20,
                            fontColor: const Color(0xFF666666),
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
        imageItem(
          image: "arabman",
          top: 200,
          left: 220,
          right: 20,
        ),
        imageItem(
          image: "arabgirl",
          top: 313,
          left: 20,
          right: 220,
        ),
        imageItem(
          image: "arabgirl-2",
          top: 410,
          left: 220,
          right: 20,
        ),
      ],
    ),
  );
}

// -------------onboardingPagesList---------------
final onboardingPagesList = [
  onBoardingItem(
      image: "arabman",
      title: "About the app",
      description:
          "Globally awarded, the Hand Talk app assists in learning and understanding sign language through artificial intelligence. "),
  onBoardingStackItem(title: "You Can Select your favorite character "),
  onBoardingItem(
      image: "arabman",
      title: "Hello",
      description:
          "Hello, I’m Omar, Hand Talk’s virtual sign language translator and i’m here to help your!"),
  onBoardingItem(
      image: "arabman",
      title: "How it Works",
      description:
          "The app uses Artifical Intelligence to translate Arabic into Sign Language. Sometimes the translations may not be perfect."),
];

// ---------------- shared preference keys ----------------

const String kEYOnboardingCompleted = "isOnBoardingCompleted";
const String kEYShowCaseCompleted = 'isShowCaseCompleted';

//---------------- avatar selection -----------------------


