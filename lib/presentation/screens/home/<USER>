import 'package:arabic_sign_language/presentation/screens/account/account_screen.dart';
import 'package:arabic_sign_language/presentation/screens/store/store_screen.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/bottom_navigation_bar.dart';
import 'package:flutter/material.dart';

ValueNotifier<int> bottomBarIndex = ValueNotifier<int>(0);

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    const List<Widget> _pages = [
      UnityScreen(),
      // StoreScreen(),
      AccountScreen(),
    ];
    return ValueListenableBuilder(
      valueListenable: bottomBarIndex,
      builder: (ctx, index, _) => Scaffold(
        backgroundColor: index == 0 ? Colors.white : Color(0xFFE7E7E7),
        body: _pages[index],
        bottomNavigationBar: const BottomBar(),
      ),
    );
  }
}
