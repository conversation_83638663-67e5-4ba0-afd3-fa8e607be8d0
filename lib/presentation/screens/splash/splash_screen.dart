import '../../core/constants.dart';
import '../home/<USER>';
import '../onboarding/onboarding_screen.dart';
import '../../widgets/splash_item.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Future.delayed(const Duration(seconds: 3), () async {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final isOnBoardingCompleted = prefs.getBool(kEYOnboardingCompleted);
      if (context.mounted) {
        Navigator.of(context).pushReplacement(MaterialPageRoute(
            builder: (ctx) => isOnBoardingCompleted == true
                ? const WelcomeScreen()
                : const OnBoardingScreen()));
      }
    });
    return const Scaffold(
      backgroundColor: appBg,
      body: Center(child: SplashItem()),
    );
  }
}
