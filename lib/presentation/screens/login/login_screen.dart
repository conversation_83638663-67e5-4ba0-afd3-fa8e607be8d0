import 'package:arabic_sign_language/presentation/core/constants.dart';
import 'package:arabic_sign_language/presentation/core/themes/text_theme.dart';
import 'package:arabic_sign_language/presentation/screens/home/<USER>';
import 'package:arabic_sign_language/presentation/widgets/appbar_widget.dart';
import 'package:country_code_picker/country_code_picker.dart';
import 'package:flutter/material.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Scaffold(
      backgroundColor: appBg,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(kToolbarHeight),
        child: AppBarWidget(
          isFromLogin: true,
        ),
      ),
      body: Container(
        // height: MediaQuery.sizeOf(context).height,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(50),
          ),
        ),
        child: Form(
          key: _formKey,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: SizedBox(
                height: MediaQuery.sizeOf(context).height * 0.89,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 37),
                    Text(
                      "Login",
                      style: AppTextTheme.loginScreenText(
                        fontSize: 20,
                        fontFamily: FONT_FAMILY,
                        fontColor: Color(0xFF343434),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 34),
                    Text(
                      "Enter your name",
                      style: AppTextTheme.loginScreenText(
                          fontSize: 14, fontColor: Color(0xFF666666)),
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        label: Text(
                          "Enter your Name",
                          style: AppTextTheme.loginScreenText(
                              fontSize: 10, fontColor: Color(0xFF929292)),
                        ),
                        focusedBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                        enabledBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                      ),
                    ),
                    const SizedBox(height: 17),
                    Text(
                      "Enter Your Phone Number",
                      style: AppTextTheme.loginScreenText(
                          fontSize: 14, fontColor: Color(0xFF666666)),
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      decoration: InputDecoration(
                        prefixIcon: CountryCodePicker(
                          showDropDownButton: true,
                          initialSelection: "sa",
                          onChanged: (value) {
                            print("value => $value");
                          },
                          searchDecoration:
                              const InputDecoration(label: Text("Enter Code")),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        label: Text(
                          "Enter your Phone Number",
                          style: AppTextTheme.loginScreenText(
                              fontSize: 10, fontColor: Color(0xFF929292)),
                        ),
                        focusedBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                        enabledBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                      ),
                    ),
                    const SizedBox(height: 17),
                    Text(
                      "Enter Your Email Id",
                      style: AppTextTheme.loginScreenText(
                          fontSize: 14, fontColor: Color(0xFF666666)),
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        label: Text(
                          "Enter Your Email Id",
                          style: AppTextTheme.loginScreenText(
                              fontSize: 10, fontColor: Color(0xFF929292)),
                        ),
                        focusedBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                        enabledBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                      ),
                    ),
                    const SizedBox(height: 17),
                    Text(
                      "Enter Your Nationality",
                      style: AppTextTheme.loginScreenText(
                          fontSize: 14, fontColor: Color(0xFF666666)),
                    ),
                    const SizedBox(height: 10),
                    TextFormField(
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        label: Text(
                          "Enter Your Nationality",
                          style: AppTextTheme.loginScreenText(
                              fontSize: 10, fontColor: Color(0xFF929292)),
                        ),
                        focusedBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                        enabledBorder: const OutlineInputBorder(
                            borderSide: BorderSide(color: Color(0xFFE4E4E4))),
                      ),
                    ),
                    const Spacer(),
                    SizedBox(
                      width: size.width,
                      child: ElevatedButton(
                        style: const ButtonStyle(
                            backgroundColor:
                                MaterialStatePropertyAll(guestButtonColor)),
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (ctx) => const CharacterSelection(),
                            ),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Text("Guest Login",
                              style: AppTextTheme.loginScreenText(
                                fontSize: 18,
                                fontColor: Colors.white,
                                fontFamily: FONT_FAMILY,
                                fontWeight: FontWeight.w500,
                              )),
                        ),
                      ),
                    ),
                    const SizedBox(height: 18),
                    SizedBox(
                      width: size.width,
                      child: ElevatedButton(
                        style: ButtonStyle(
                            backgroundColor:
                                MaterialStatePropertyAll(disabledButtonColor)),
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (ctx) => const CharacterSelection(),
                            ),
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 15),
                          child: Text("Continue",
                              style: AppTextTheme.loginScreenText(
                                fontSize: 18,
                                fontColor: Colors.white,
                                fontFamily: FONT_FAMILY,
                                fontWeight: FontWeight.w500,
                              )),
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
