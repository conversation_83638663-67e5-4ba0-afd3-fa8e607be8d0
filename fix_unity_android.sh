#!/bin/bash

# Quick Unity Android Build Fix
echo "🔧 Quick Unity Android Build Fix"
echo "================================"

# Set environment variables for Android development
export ANDROID_NDK_ROOT="/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/NDK"
export ANDROID_SDK_ROOT="/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/SDK"
export JAVA_HOME="/Applications/Unity/Hub/Editor/2022.3.21f1/PlaybackEngines/AndroidPlayer/OpenJDK"

echo "✅ Environment variables set:"
echo "   ANDROID_NDK_ROOT=$ANDROID_NDK_ROOT"
echo "   ANDROID_SDK_ROOT=$ANDROID_SDK_ROOT"
echo "   JAVA_HOME=$JAVA_HOME"

# Navigate to Unity project
cd unity/UnityProject

# Clear problematic cache
echo "🧹 Clearing Unity cache..."
rm -rf Library/Bee
rm -rf Library/BuildPlayerData
rm -rf Temp

# Try a simple debug build first
echo "🔨 Attempting Debug build..."
/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/MacOS/Unity \
    -batchmode -quit -projectPath . \
    -executeMethod FlutterUnityIntegration.Editor.Build.DoBuildAndroidLibraryDebug \
    -logFile debug_build.log

if [ $? -eq 0 ]; then
    echo "✅ Debug build successful!"
    echo "📁 Checking output..."
    ls -la ../../android/unityLibrary/ 2>/dev/null || echo "No output directory yet"
else
    echo "❌ Debug build failed. Checking errors..."
    if [ -f "debug_build.log" ]; then
        echo "--- Key errors from debug build ---"
        grep -i "error\|exception\|failed" debug_build.log | tail -10
    fi
fi

echo "🏁 Quick fix completed"
