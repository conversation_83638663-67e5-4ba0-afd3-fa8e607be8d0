#!/bin/bash

# Test script for Unity Android build
echo "Testing Unity Android Build..."

# Check if Unity is running
if pgrep -f "Unity" > /dev/null; then
    echo "ERROR: Unity is currently running. Please close Unity Editor first."
    exit 1
fi

# Navigate to Unity project
cd unity/UnityProject

# Check if Android Build Support is installed
UNITY_PATH="/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/MacOS/Unity"
ANDROID_SUPPORT_PATH="/Applications/Unity/Hub/Editor/2022.3.21f1/Unity.app/Contents/PlaybackEngines/AndroidPlayer"

if [ ! -d "$ANDROID_SUPPORT_PATH" ]; then
    echo "ERROR: Android Build Support is not installed."
    echo "Please install it via Unity Hub:"
    echo "1. Open Unity Hub"
    echo "2. Go to Installs tab"
    echo "3. Click gear icon for Unity 2022.3.21f1"
    echo "4. Select 'Add modules'"
    echo "5. Check 'Android Build Support'"
    echo "6. Install"
    exit 1
fi

echo "Android Build Support found. Attempting build..."

# Run Unity build
"$UNITY_PATH" -batchmode -quit -projectPath . \
    -executeMethod FlutterUnityIntegration.Editor.Build.DoBuildAndroidLibraryRelease \
    -logFile build.log

# Check build result
if [ $? -eq 0 ]; then
    echo "Build completed successfully!"
    echo "Check android/unityLibrary/ for output"
else
    echo "Build failed. Check build.log for details:"
    if [ -f "build.log" ]; then
        tail -20 build.log
    fi
fi
