import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:arabic_sign_language/bloc/youtubeScreen/youtube_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';

class MockTranscriptService extends Mock implements TranscriptService {}

void main() {
  late MockTranscriptService transcriptService;
  late YoutubeScreenBloc bloc;

  setUp(() {
    transcriptService = MockTranscriptService();
    bloc = YoutubeScreenBloc(transcriptService);
  });

  tearDown(() {
    bloc.close();
  });

  test('initial state is YoutubeScreenInitial', () {
    expect(bloc.state, YoutubeScreenInitial());
  });

  blocTest<YoutubeScreenBloc, YoutubeScreenState>(
    'emits UpdateCurrentAnimation when UpdateCurrentAnimationText is added',
    build: () => bloc,
    act: (bloc) => bloc.add(UpdateCurrentAnimationText(animation: 'test')),
    expect: () => [UpdateCurrentAnimation(currentAnimation: 'test')],
  );

  blocTest<YoutubeScreenBloc, YoutubeScreenState>(
    'emits YoutubeSliderValueUpdated when YoutubeUpdateSliderValue is added',
    build: () => bloc,
    act: (bloc) => bloc.add(YoutubeUpdateSliderValue(value: 0.5)),
    expect: () => [YoutubeSliderValueUpdated(sliderValue: 0.5)],
  );

  blocTest<YoutubeScreenBloc, YoutubeScreenState>(
    'emits YoutubeScreenError when SendErrorMessage is added',
    build: () => bloc,
    act: (bloc) => bloc.add(SendErrorMessage(message: 'error')),
    expect: () => [YoutubeScreenError(message: 'error')],
  );

  blocTest<YoutubeScreenBloc, YoutubeScreenState>(
    'emits SendMessageToUnity when SendMessage is added',
    build: () => bloc,
    act: (bloc) => bloc.add(SendMessage(message: {'root': 'a', 'word': 'b'})),
    expect: () => [
      SendMessageToUnity(
        message: {'root': 'a', 'word': 'b'},
        currentAnimation: '',
      )
    ],
  );
}
