// Dart
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:arabic_sign_language/bloc/UnityScreen/unity_screen_bloc.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';
import 'package:flutter/material.dart';

class MockRecordingService extends Mock implements RecordingService {}

class MockTextConversionService extends Mock implements TextConversionService {}

class MockTranscriptService extends Mock implements TranscriptService {}

class MockTextEditingController extends Mock implements TextEditingController {}

class FakeFocusNode extends Fake implements FocusNode {
  @override
  void unfocus({UnfocusDisposition disposition = UnfocusDisposition.scope}) {
    // Do nothing
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'FakeFocusNode';
  }
}

void main() {
  late RecordingService recordingService;
  late TextConversionService textConversionService;
  late TranscriptService transcriptService;

  setUp(() {
    recordingService = MockRecordingService();
    textConversionService = MockTextConversionService();
    transcriptService = MockTranscriptService();
  });

  group('UnityScreenBloc', () {
    test('initial state is UnityScreenInitial', () {
      final bloc = UnityScreenBloc(
          recordingService, textConversionService, transcriptService);
      expect(bloc.state, isA<UnityScreenInitial>());
    });

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits VisibilityChanged when ToggleVisibility is added',
      build: () => UnityScreenBloc(
          recordingService, textConversionService, transcriptService),
      act: (bloc) => bloc
          .add(ToggleVisibility(isVisible: true, isDictionaryOverlay: false)),
      expect: () => [
        isA<VisibilityChanged>().having((s) => s.isVisible, 'isVisible', true),
      ],
    );

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits EnableRecording when ChangeRecorderStatus is added',
      build: () => UnityScreenBloc(
          recordingService, textConversionService, transcriptService),
      act: (bloc) => bloc.add(ChangeRecorderStatus(isRecordingEnabled: true)),
      expect: () => [
        isA<EnableRecording>()
            .having((s) => s.isRecordingEnabled, 'isRecordingEnabled', true),
      ],
    );

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits UpdateTextFieldStatus when ChangeTextFieldStatus is added',
      build: () => UnityScreenBloc(
          recordingService, textConversionService, transcriptService),
      act: (bloc) => bloc.add(ChangeTextFieldStatus(status: true)),
      expect: () => [
        isA<UpdateTextFieldStatus>()
            .having((s) => s.isTextFieldEnabled, 'isTextFieldEnabled', true),
      ],
    );

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits SliderValueUpdated when UpdateSliderValue is added',
      build: () => UnityScreenBloc(
          recordingService, textConversionService, transcriptService),
      act: (bloc) => bloc.add(UpdateSliderValue(value: 0.5)),
      expect: () => [
        isA<SliderValueUpdated>()
            .having((s) => s.sliderValue, 'sliderValue', 0.5),
      ],
    );

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits UpdateDictionarySearch with suggestions when DictionarySearch is added with empty value',
      build: () => UnityScreenBloc(
          recordingService, textConversionService, transcriptService),
      act: (bloc) => bloc.add(DictionarySearch(value: "")),
      expect: () => [
        isA<UpdateDictionarySearch>(),
      ],
    );

    blocTest<UnityScreenBloc, UnityScreenState>(
      'emits UnityScreenError when ProcessTranscription is added and service returns empty',
      build: () {
        when(() => textConversionService.getTranscribedText(any()))
            .thenAnswer((_) async => []);
        return UnityScreenBloc(
            recordingService, textConversionService, transcriptService);
      },
      act: (bloc) => bloc.add(ProcessTranscription(
        inputText: "test",
        inputTextController: MockTextEditingController(),
        inputFocusNode: FakeFocusNode(),
      )),
      expect: () => [
        isA<DisableSendButtonAction>(),
        isA<UnityScreenError>(),
      ],
    );
  });
} // Dart
