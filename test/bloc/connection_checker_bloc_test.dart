import 'package:arabic_sign_language/bloc/connectionChecker/connection_checker_bloc.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ConnectionCheckerBloc', () {
    test('initial state is correct', () {
      final bloc = ConnectionCheckerBloc();
      expect(bloc.state, ConnectionCheckerInitial());
    });

    test(
        'emits [ConnectivityStatusChanged(true)] when connection is established',
        () {
      final bloc = ConnectionCheckerBloc();
      bloc.add(ConnectivityChanged(status: true));
      expectLater(
        bloc.stream,
        emitsInOrder([ConnectivityStatusChanged(isNetworkConnected: true)]),
      );
    });

    test('emits [ConnectivityStatusChanged(false)] when connection fails', () {
      final bloc = ConnectionCheckerBloc();
      bloc.add(ConnectivityChanged(status: false));
      expectLater(
        bloc.stream,
        emitsInOrder([ConnectivityStatusChanged(isNetworkConnected: false)]),
      );
    });
  });
}
