import 'package:flutter_test/flutter_test.dart';
import 'package:arabic_sign_language/data/service/app_version.dart';

void main() {
  group('AppVersionService.compareVersions', () {
    test('returns 0 when versions are equal', () {
      expect(AppVersionService.compareVersions('1.2.3', '1.2.3'), 0);
      expect(AppVersionService.compareVersions('1.0', '1.0.0'), 0);
    });

    test('returns 1 when first version is greater', () {
      expect(AppVersionService.compareVersions('1.2.4', '1.2.3'), 1);
      expect(AppVersionService.compareVersions('2.0.0', '1.9.9'), 1);
      expect(AppVersionService.compareVersions('1.2.0', '1.1.9'), 1);
    });

    test('returns -1 when first version is less', () {
      expect(AppVersionService.compareVersions('1.2.3', '1.2.4'), -1);
      expect(AppVersionService.compareVersions('1.9.9', '2.0.0'), -1);
      expect(AppVersionService.compareVersions('1.1.9', '1.2.0'), -1);
    });

    test('handles versions with different lengths', () {
      expect(AppVersionService.compareVersions('1.2', '1.2.1'), -1);
      expect(AppVersionService.compareVersions('1.2.1', '1.2'), 1);
      expect(AppVersionService.compareVersions('1', '1.0.0'), 0);
    });
  });
}
