import 'dart:convert';
import 'package:arabic_sign_language/data/models/video_transcription/audio_session.dart';
import 'package:arabic_sign_language/data/models/video_transcription/video_session.dart';
import 'package:arabic_sign_language/data/models/video_transcription/audio_status/audio_status.dart';
import 'package:arabic_sign_language/data/models/video_transcription/transcription_item.dart';
import 'package:arabic_sign_language/data/service/transcript_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDio extends Mock implements Dio {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  late MockDio mockDio;
  late TranscriptService service;

  setUp(() {
    mockDio = MockDio();
    service = TranscriptService(dio: mockDio);
  });

  test('startTranscription returns VideoSession on success', () async {
    final response = Response(
      data: {
        'status': true,
        'videoUrl': 'https://www.youtube.com/watch?v=dinQIb4ZFXY',
        'sessionId': 1
      },
      statusCode: 200,
      requestOptions: RequestOptions(path: ''),
    );
    when(() => mockDio.post(any(), data: any(named: 'data')))
        .thenAnswer((_) async => response);

    final result = await service
        .startTranscription('https://www.youtube.com/watch?v=dinQIb4ZFXY');
    expect(result, isA<VideoSession>());
    expect(result.status, true);
    expect(result.sessionId, 1);
    expect(result.videoUrl, 'https://www.youtube.com/watch?v=dinQIb4ZFXY');
  });

  // test('getTranscript returns CaptionData on success', () async {
  //   final response = Response(
  //     data: {
  //       'status': true,
  //       'data': [
  //         {
  //           'id': 1,
  //           'video_id': 1,
  //           'start_time': 0.0,
  //           'end_time': 1.0,
  //           'word': ['hello'],
  //           'root': ['hello'],
  //           'status': 'COMPLETED'
  //         }
  //       ]
  //     },
  //     statusCode: 200,
  //     requestOptions: RequestOptions(path: ''),
  //   );
  //   when(() => mockDio.post(any(), data: any(named: 'data')))
  //       .thenAnswer((_) async => response);

  //   final result = await service.getTranscript(1);
  //   expect(result, isA<CaptionData>());
  //   expect(result.data, isNotEmpty);
  //   expect(result.data.first.word.first, 'hello');
  //   expect(result.data.first.status, 'COMPLETED');
  // });

  // test('getVideoStatus returns AudioStatus on success', () async {
  //   final response = Response(
  //     data: {
  //       'status': true,
  //       'session_id': 1,
  //     },
  //     statusCode: 200,
  //     requestOptions: RequestOptions(path: ''),
  //   );
  //   when(() =>
  //           mockDio.get(any(), queryParameters: any(named: 'queryParameters')))
  //       .thenAnswer((_) async => response);

  //   final result = await service.getVideoStatus('url');
  //   expect(result, isA<AudioSession>());
  //   expect(result.status, true);
  //   expect(result.sessionId, 1);
  // });
}
