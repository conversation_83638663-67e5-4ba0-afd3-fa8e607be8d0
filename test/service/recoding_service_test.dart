import 'dart:typed_data';

import 'package:arabic_sign_language/data/models/recording_model/recording_model.dart';
import 'package:arabic_sign_language/data/service/recoding_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDio extends Mock implements Dio {}

class MockResponse extends Mock implements Response {}

void main() {
  late RecordingService service;
  late MockDio mockDio;

  setUp(() {
    mockDio = MockDio();
    service = RecordingService(dio: mockDio);
  });

  test('returns list of RecordingModel on success', () async {
    final fakeData = [
      {'id': 1, 'root': 'hello'} // adjust fields as per your RecordingModel
    ];
    final response = Response(
      data: {'status': true, 'data': fakeData},
      statusCode: 200,
      requestOptions: RequestOptions(path: ''),
    );
    when(() => mockDio.post(any(), data: any(named: 'data')))
        .thenAnswer((_) async => response);

    final result = await service.getTranscriptionFromRecording(Uint8List(0));
    expect(result, isA<List<RecordingModel>>());
    expect(result.first.root, 'hello'); // adjust as per your model
  });

  test('returns empty list on error', () async {
    when(() => mockDio.post(any(), data: any(named: 'data')))
        .thenThrow(Exception('error'));

    final result = await service.getTranscriptionFromRecording(Uint8List(0));
    expect(result, isEmpty);
  });
}
