import 'package:arabic_sign_language/data/models/text_transcription_model/text_transcription_model.dart';
import 'package:arabic_sign_language/data/service/text_conversion_service.dart';
import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDio extends Mock implements Dio {}

void main() {
  late MockDio mockDio;
  late TextConversionService service;

  setUp(() {
    mockDio = MockDio();
    service = TextConversionService(dio: mockDio);
  });

  test('returns list of TextTranscriptionModel on success', () async {
    final fakeData = [
      {
        'root': ['hello'],
        'word': ['hello']
      } // adjust as per your model
    ];
    final response = Response(
      data: {'status': true, 'data': fakeData},
      statusCode: 200,
      requestOptions: RequestOptions(path: ''),
    );
    when(() => mockDio.post(any(), data: any(named: 'data')))
        .thenAnswer((_) async => response);

    final result = await service.getTranscribedText('test');
    expect(result, isA<List<TextTranscriptionModel>>());
    expect(result.first.root.first, 'hello'); // adjust as per your model
  });

  test('returns empty list on error', () async {
    when(() => mockDio.post(any(), data: any(named: 'data')))
        .thenThrow(Exception('error'));

    final result = await service.getTranscribedText('test');
    expect(result, isEmpty);
  });
}
